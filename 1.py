import os
import time
import smtplib
import logging
from email.mime.text import MIMEText
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

# ======================= 配置区 (请根据需要修改) =======================
class Config:
    # --- 核心凭证 ---
    COOKIE_STRING = "acw_sc__v2=689545964deb31dc2d778784747a5434e2880635; session=MTc1NDYxMzE0OHxEWDhFQVFMX2dBQUJFQUVRQUFEX3dfLUFBQVlHYzNSeWFXNW5EQWNBQldkeWIzVndCbk4wY21sdVp3d0pBQWRrWldaaGRXeDBCbk4wY21sdVp3d05BQXR2WVhWMGFGOXpkR0YwWlFaemRISnBibWNNRGdBTVNqQTJkMVpHVFV4dmFFeExCbk4wY21sdVp3d0VBQUpwWkFOcGJuUUVCQUQtU1J3R2MzUnlhVzVuREFvQUNIVnpaWEp1WVcxbEJuTjBjbWx1Wnd3TkFBdG5hWFJvZFdKZk9UTTFPQVp6ZEhKcGJtY01CZ0FFY205c1pRTnBiblFFQWdBQ0JuTjBjbWx1Wnd3SUFBWnpkR0YwZFhNRGFXNTBCQUlBQWc9PXy0sH4Nqm21gngcSzXEpq0F4lRIUGh1oUufZQjVaKmJIA=="
    EMAIL_ACCOUNT = "<EMAIL>"
    EMAIL_PASSWORD = "yijxghlfngmundrk" # ‼️ 粘贴你生成的16位应用专用密码
    RECIPIENT_EMAIL = "<EMAIL>"

    # --- 网站和元素定位器 (如果网站更新, 可能需要修改这里) ---
    LOGIN_URL = "https://anyrouter.top/login"
    TARGET_URL = "https://anyrouter.top/console/topup"
    # ✨ 新增: 定位“系统公告”弹窗的关闭按钮
    ANNOUNCEMENT_CLOSE_BUTTON_XPATH = "//button[.//span[text()='关闭公告']]"
    # 用于验证登录是否成功的元素, 例如包含用户邮箱或"Logout"的元素
    LOGIN_VERIFY_ELEMENT_XPATH = '//div[contains(text(), "Logout")]' 
    BALANCE_ELEMENT_XPATH = '//div[contains(@class, "text-xl") and contains(text(), "$")]'
    
    # --- 运行参数 ---
    MAX_RETRIES = 3 
    RETRY_DELAY_SECONDS = 5
    RECORD_FILE = "balance_record.txt"
    LOG_FILE = "balance_checker.log"

# =========================================================================
# setup_logging, send_email, get_last_balance, write_new_balance, save_failure_snapshot 函数保持不变
# 为了简洁，这里省略这些函数的代码，请在你自己的文件中保留它们
# ...

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(Config.LOG_FILE, mode='w'), # mode='w' 每次覆盖日志
            logging.StreamHandler()
        ]
    )

def send_email(subject, body, files=None):
    """发送邮件通知"""
    try:
        msg = MIMEText(body, 'plain', 'utf-8')
        msg['From'] = Config.EMAIL_ACCOUNT
        msg['To'] = Config.RECIPIENT_EMAIL
        msg['Subject'] = subject

        with smtplib.SMTP("smtp.gmail.com", 587) as server:
            server.starttls()
            server.login(Config.EMAIL_ACCOUNT, Config.EMAIL_PASSWORD)
            server.sendmail(Config.EMAIL_ACCOUNT, [Config.RECIPIENT_EMAIL], msg.as_string())
        logging.info("邮件发送成功！")
    except Exception as e:
        logging.error(f"邮件发送失败: {e}")

def get_last_balance(file_path):
    """从文件中读取最近一次记录的余额"""
    if not os.path.exists(file_path): return None
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
            if not lines: return None
            last_line = lines[-1].strip()
            last_balance_str = last_line.split('$')[-1]
            return float(last_balance_str)
    except (IOError, IndexError, ValueError) as e:
        logging.warning(f"读取历史余额失败: {e}")
        return None

def write_new_balance(file_path, new_balance):
    """将新的余额和时间戳追加写入文件"""
    try:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with open(file_path, 'a') as f:
            f.write(f"{timestamp} - ${new_balance:.2f}\n")
        logging.info(f"记录成功: {timestamp} - ${new_balance:.2f}")
    except IOError as e:
        logging.error(f"写入余额记录失败: {e}")

def save_failure_snapshot(driver, prefix="failure"):
    """在失败时保存截图和页面源码"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_file = f"{prefix}_{timestamp}.png"
    html_file = f"{prefix}_{timestamp}.html"
    try:
        driver.save_screenshot(screenshot_file)
        logging.info(f"已保存失败截图至: {screenshot_file}")
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        logging.info(f"已保存页面源码至: {html_file}")
        return screenshot_file, html_file
    except Exception as e:
        logging.error(f"保存失败快照时出错: {e}")
        return None, None

def perform_check():
    """
    执行一次完整的检查流程, 包括登录、验证和抓取.
    如果成功, 返回余额 (float).
    如果失败, 抛出异常.
    """
    options = webdriver.ChromeOptions()
    options.add_argument("--headless")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
    
    with webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options) as driver:
        # 1. 注入Cookie
        logging.info(f"正在访问: {Config.LOGIN_URL} 以建立会话...")
        driver.get(Config.LOGIN_URL)

        logging.info("正在注入Cookie...")
        driver.delete_all_cookies()
        cookies = [cookie.strip().split('=', 1) for cookie in Config.COOKIE_STRING.split(';')]
        for name, value in cookies:
            driver.add_cookie({'name': name, 'value': value, 'domain': '.anyrouter.top'})
        
        # 2. 访问目标页面
        logging.info(f"正在打开目标页面: {Config.TARGET_URL}")
        driver.get(Config.TARGET_URL)

        # ✨✨✨ === 新增的核心逻辑：处理公告弹窗 === ✨✨✨
        try:
            logging.info("正在检查是否有系统公告弹窗...")
            close_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, Config.ANNOUNCEMENT_CLOSE_BUTTON_XPATH))
            )
            logging.info("发现公告弹窗，正在点击 '关闭公告' 按钮...")
            close_button.click()
            time.sleep(2) # 给予弹窗动画关闭的时间
        except TimeoutException:
            logging.info("未发现系统公告弹窗，或弹窗已关闭，继续执行。")
        # ✨✨✨ ======================================= ✨✨✨

        # 3. 验证登录状态
        try:
            logging.info(f"正在验证登录状态，寻找元素: {Config.LOGIN_VERIFY_ELEMENT_XPATH}")
            WebDriverWait(driver, 15).until(
                EC.visibility_of_element_located((By.XPATH, Config.LOGIN_VERIFY_ELEMENT_XPATH))
            )
            logging.info("登录验证成功！看起来Cookie是有效的。")
        except TimeoutException:
            logging.error("登录验证失败！无法在页面上找到验证元素。")
            logging.error("这通常意味着Cookie已失效、页面结构已改变、或被其他弹窗阻挡。")
            save_failure_snapshot(driver, "login_fail")
            raise Exception("LoginVerificationFailed")

        # 4. 抓取余额
        try:
            logging.info(f"正在抓取余额，寻找元素: {Config.BALANCE_ELEMENT_XPATH}")
            balance_div = WebDriverWait(driver, 15).until(
                EC.visibility_of_element_located((By.XPATH, Config.BALANCE_ELEMENT_XPATH))
            )
            current_balance_str = balance_div.text.replace('$', '').strip()
            current_balance = float(current_balance_str)
            logging.info(f"成功获取当前余额: ${current_balance:.2f}")
            return current_balance
        except TimeoutException:
            logging.error("抓取余额失败！在页面上找不到余额元素。")
            save_failure_snapshot(driver, "balance_scrape_fail")
            raise Exception("BalanceScrapeFailed")


def main():
    """主执行函数, 包含重试逻辑"""
    setup_logging()
    
    if "在这里替换" in Config.EMAIL_PASSWORD:
        logging.error("错误：请先在脚本的Config类中配置你的Gmail应用密码 (EMAIL_PASSWORD)。")
        return

    last_balance = get_last_balance(Config.RECORD_FILE)
    logging.info(f"读取到上一次的余额: ${last_balance}" if last_balance is not None else "未找到历史余额记录，将进行首次记录。")

    current_balance = None
    for attempt in range(1, Config.MAX_RETRIES + 1):
        try:
            logging.info(f"--- 第 {attempt}/{Config.MAX_RETRIES} 次尝试 ---")
            current_balance = perform_check()
            break 
        except Exception as e:
            logging.error(f"第 {attempt} 次尝试失败: {e}")
            if attempt < Config.MAX_RETRIES:
                logging.info(f"将在 {Config.RETRY_DELAY_SECONDS} 秒后重试...")
                time.sleep(Config.RETRY_DELAY_SECONDS)
            else:
                logging.error("已达到最大重试次数，任务最终失败。")
                send_email("❌ 监控脚本最终失败", f"你好 Yuze,\n\n脚本在尝试 {Config.MAX_RETRIES} 次后最终失败。\n\n最后的错误是: {e}\n\n请查看服务器上的日志文件 {Config.LOG_FILE} 和失败截图获取详细信息。\n\n时间: {datetime.now()}")
                return

    if current_balance is not None:
        write_new_balance(Config.RECORD_FILE, current_balance)
        
        if last_balance is not None:
            if round(current_balance - last_balance, 2) == 25.00:
                subject = "✅ 成功: 余额增加$25"
                body = (f"你好 Yuze,\n\n检测到您的 AnyRouter 账户余额已成功增加 $25。\n\n"
                        f"上一次余额: ${last_balance:.2f}\n"
                        f"当前最新余额: ${current_balance:.2f}")
                send_email(subject, body)
            else:
                subject = "提醒: 余额未按预期增加$25"
                body = (f"你好 Yuze,\n\n本次检查发现您的 AnyRouter 账户余额未按预期增加 $25。\n\n"
                        f"上一次余额: ${last_balance:.2f}\n"
                        f"当前最新余额: ${current_balance:.2f}")
                send_email(subject, body)
        else:
            subject = "🚀 首次运行通知"
            body = (f"你好 Yuze,\n\n这是余额监控脚本的首次运行。\n\n"
                    f"已记录您的初始余额为: ${current_balance:.2f}")
            send_email(subject, body)

if __name__ == '__main__':
    main()